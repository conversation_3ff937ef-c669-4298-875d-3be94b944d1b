/**
 * Parser Component - Analyzes OpenAPI specifications and generates function tools
 * Inspired by RestGPT's Parser that generates code based on response schemas
 */
import { APIEndpoint, FunctionTool, OpenAPISpec } from "./types";

export class APISpecParser {
  /**
   * Parse OpenAPI specification and extract all endpoints
   */
  parseOpenAPISpec(spec: OpenAPISpec): APIEndpoint[] {
    const endpoints: APIEndpoint[] = [];

    for (const [path, pathItem] of Object.entries(spec.paths)) {
      for (const [method, operation] of Object.entries(pathItem)) {
        if (typeof operation === "object" && operation !== null) {
          const endpoint: APIEndpoint = {
            path,
            method: method.toUpperCase(),
            operationId: operation.operationId,
            summary: operation.summary,
            description: operation.description,
            parameters: operation.parameters || [],
            requestBody: operation.requestBody,
            responses: operation.responses || {},
          };
          endpoints.push(endpoint);
        }
      }
    }

    return endpoints;
  }

  /**
   * Generate function tools from API endpoints
   */
  generateFunctionTools(
    endpoints: APIEndpoint[],
    baseUrl?: string,
  ): FunctionTool[] {
    return endpoints.map((endpoint, index) => {
      const toolName = this.generateToolName(endpoint);
      const description = this.generateToolDescription(endpoint);
      const parameters = this.extractParameters(endpoint);

      return {
        id: `tool_${index + 1}`,
        name: toolName,
        method: endpoint.method,
        description,
        endpoint,
        parameters,
        enabled: true,
        userModified: false,
      };
    });
  }

  /**
   * Generate a meaningful tool name from endpoint
   */
  private generateToolName(endpoint: APIEndpoint): string {
    if (endpoint.operationId) {
      return this.camelToSnakeCase(endpoint.operationId);
    }

    const pathParts = endpoint.path
      .split("/")
      .filter((part) => part && !part.startsWith("{"));
    const method = endpoint.method.toLowerCase();

    if (pathParts.length > 0) {
      const resource = pathParts[pathParts.length - 1];
      return `${method}_${resource}`;
    }

    return `${method}_endpoint`;
  }

  /**
   * Generate a descriptive tool description
   */
  private generateToolDescription(endpoint: APIEndpoint): string {
    if (endpoint.description) {
      return endpoint.description;
    }

    if (endpoint.summary) {
      return endpoint.summary;
    }

    const method = endpoint.method.toUpperCase();
    const path = endpoint.path;
    return `${method} ${path}`;
  }

  /**
   * Extract and convert parameters to function tool format
   */
  private extractParameters(endpoint: APIEndpoint): {
    type: "object";
    properties: Record<string, any>;
    required?: string[];
  } {
    const properties: Record<string, any> = {};
    const required: string[] = [];

    // Process path parameters
    if (endpoint.parameters) {
      for (const param of endpoint.parameters) {
        properties[param.name] = {
          type: this.getSchemaType(param.schema),
          description: param.description || `${param.in} parameter`,
          ...(param.schema || {}),
        };

        if (param.required) {
          required.push(param.name);
        }
      }
    }

    // Process request body
    if (endpoint.requestBody) {
      const content = endpoint.requestBody.content;
      if (content["application/json"]) {
        const schema = content["application/json"].schema;
        if (schema && schema.properties) {
          Object.assign(properties, schema.properties);
          if (schema.required) {
            required.push(...schema.required);
          }
        }
      }
    }

    return {
      type: "object",
      properties,
      ...(required.length > 0 && { required }),
    };
  }

  /**
   * Get OpenAPI schema type
   */
  private getSchemaType(schema: any): string {
    if (!schema) return "string";
    return schema.type || "string";
  }

  /**
   * Convert camelCase to snake_case
   */
  private camelToSnakeCase(str: string): string {
    return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
  }

  /**
   * Parse API response using schema information
   * This method generates parsing instructions for the Executor
   */
  generateParsingInstructions(
    endpoint: APIEndpoint,
    outputGoal: string,
  ): string {
    const responses = endpoint.responses;
    const successResponse =
      responses["200"] || responses["201"] || responses["default"];

    if (!successResponse || !successResponse.content) {
      return `Extract relevant information from the response that helps achieve: ${outputGoal}`;
    }

    const schema = successResponse.content["application/json"]?.schema;
    if (!schema) {
      return `Parse the JSON response and extract information relevant to: ${outputGoal}`;
    }

    let instructions = `Parse the JSON response according to the following schema and extract information for: ${outputGoal}\n`;

    if (schema.properties) {
      instructions += "Expected response structure:\n";
      for (const [key, value] of Object.entries(schema.properties)) {
        const prop = value as any;
        instructions += `- ${key}: ${prop.type || "unknown"} - ${prop.description || "No description"}\n`;
      }
    }

    return instructions;
  }

  /**
   * Validate OpenAPI specification
   */
  validateOpenAPISpec(spec: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!spec.openapi && !spec.swagger) {
      errors.push("Missing openapi or swagger version field");
    }

    if (!spec.info) {
      errors.push("Missing info object");
    } else {
      if (!spec.info.title) errors.push("Missing info.title");
      if (!spec.info.version) errors.push("Missing info.version");
    }

    if (!spec.paths || Object.keys(spec.paths).length === 0) {
      errors.push("Missing or empty paths object");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
