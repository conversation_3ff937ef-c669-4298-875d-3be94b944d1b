/**
 * API Route: Upload and process OpenAPI specification
 * Replaces Papermark's document upload functionality
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { createRestGPTEngine } from '../../../lib/restgpt-engine';
import formidable from 'formidable';
import fs from 'fs';

export const config = {
  api: {
    bodyParser: false,
  },
};

interface ApiSpecUploadRequest {
  teamId?: string;
  name: string;
  description?: string;
  baseUrl: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Parse form data
    const form = formidable({
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
    });

    const [fields, files] = await form.parse(req);
    
    const name = Array.isArray(fields.name) ? fields.name[0] : fields.name;
    const description = Array.isArray(fields.description) ? fields.description[0] : fields.description;
    const baseUrl = Array.isArray(fields.baseUrl) ? fields.baseUrl[0] : fields.baseUrl;
    const teamId = Array.isArray(fields.teamId) ? fields.teamId[0] : fields.teamId;

    if (!name || !baseUrl) {
      return res.status(400).json({ error: 'Name and base URL are required' });
    }

    // Get uploaded file
    const uploadedFile = Array.isArray(files.spec) ? files.spec[0] : files.spec;
    if (!uploadedFile) {
      return res.status(400).json({ error: 'No OpenAPI specification file uploaded' });
    }

    // Read and parse the OpenAPI spec
    const fileContent = fs.readFileSync(uploadedFile.filepath, 'utf8');
    let openApiSpec;
    
    try {
      openApiSpec = JSON.parse(fileContent);
    } catch (error) {
      try {
        // Try YAML parsing if JSON fails
        const yaml = require('yaml');
        openApiSpec = yaml.parse(fileContent);
      } catch (yamlError) {
        return res.status(400).json({ error: 'Invalid OpenAPI specification format' });
      }
    }

    // Initialize RestGPT engine to validate and process the spec
    const engine = createRestGPTEngine({
      openaiApiKey: process.env.OPENAI_API_KEY!,
      enableLogging: true
    });

    const initResult = await engine.initialize(openApiSpec, baseUrl);
    
    if (!initResult.success) {
      return res.status(400).json({ 
        error: 'Invalid OpenAPI specification',
        details: initResult.errors 
      });
    }

    // TODO: Save to database (will be implemented when we update the schema)
    // For now, return the processed tools
    
    res.status(200).json({
      success: true,
      message: 'API specification uploaded and processed successfully',
      data: {
        id: `spec_${Date.now()}`, // Temporary ID
        name,
        description,
        baseUrl,
        toolCount: initResult.tools.length,
        tools: initResult.tools,
        createdAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
