/**
 * API Route: Team API Specifications
 * Handles CRUD operations for API specifications within a team context
 */
import { NextApiRequest, NextApiResponse } from "next";

import { z } from "zod";

import {
  APIError,
  createSuccessResponse,
  requireAuth,
  validateBody,
  validateMethod,
  withErrorHandling,
} from "@/lib/api-utils";
import prisma from "@/lib/prisma";
import { createRestGPTEngine } from "@/lib/restgpt-engine";
import { CustomUser } from "@/lib/types";

// Validation schema for creating API specifications
const createApiSpecSchema = z.object({
  documentId: z.string().optional(),
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  description: z.string().optional(),
  baseUrl: z.string().url("Invalid base URL").optional(),
  apiVersion: z.string().optional(),
  authType: z.string().optional(),
  authConfig: z.any().optional(),
  parsedSchema: z.any().optional(),
  functionTools: z.array(z.any()).optional(),
});

async function apiSpecificationsHandler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const session = await requireAuth(req, res);
  const { teamId } = req.query;

  if (!teamId || typeof teamId !== "string") {
    throw new APIError(400, "Team ID is required", "INVALID_TEAM_ID");
  }

  if (req.method === "POST") {
    return handleCreateApiSpecification(req, res, session, teamId);
  } else if (req.method === "GET") {
    return handleListApiSpecifications(req, res, session, teamId);
  } else {
    validateMethod(req, ["POST", "GET"]);
  }
}

async function handleCreateApiSpecification(
  req: NextApiRequest,
  res: NextApiResponse,
  session: any,
  teamId: string,
) {
  const validatedData = validateBody(req, createApiSpecSchema);

  try {
    // If we have parsedSchema, use it directly; otherwise we need to parse from document
    let openApiSpec = validatedData.parsedSchema;
    let functionTools = validatedData.functionTools || [];

    if (!openApiSpec && validatedData.documentId) {
      // For API specifications, we'll skip document storage and work directly with the parsed schema
      // This avoids the need for blob storage configuration for simple API spec uploads
      throw new APIError(
        400,
        "Please provide the parsed schema directly",
        "NO_PARSED_SCHEMA",
      );
    }

    if (!openApiSpec) {
      throw new APIError(400, "No API specification provided", "NO_SPEC");
    }

    // If no function tools provided, generate them using RestGPT engine (if OpenAI API key is available)
    if (
      functionTools.length === 0 &&
      validatedData.baseUrl &&
      process.env.OPENAI_API_KEY
    ) {
      try {
        const engine = createRestGPTEngine({
          openaiApiKey: process.env.OPENAI_API_KEY,
          enableLogging: true,
        });

        const initResult = await engine.initialize(
          openApiSpec,
          validatedData.baseUrl,
        );

        if (!initResult.success) {
          console.warn("Failed to generate function tools:", initResult.errors);
          // Continue without function tools rather than failing
        } else {
          functionTools = initResult.tools;
        }
      } catch (error) {
        console.warn("Error generating function tools:", error);
        // Continue without function tools rather than failing
      }
    }

    // Create API specification
    const apiSpec = await prisma.apiSpecification.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        baseUrl: validatedData.baseUrl,
        openApiSpec: openApiSpec,
        version:
          validatedData.apiVersion ||
          openApiSpec.openapi ||
          openApiSpec.swagger,
        originalFile: validatedData.documentId
          ? "from-document"
          : "direct-input",
        fileSize: JSON.stringify(openApiSpec).length,
        status: "ACTIVE",
        enabled: true,
        toolCount: functionTools.length,
        ownerId: (session.user as CustomUser).id,
        teamId: teamId,
      },
    });

    // Save generated tools to database
    if (functionTools.length > 0) {
      const toolsData = functionTools.map((tool: any) => ({
        name: tool.name,
        description: tool.description,
        enabled: true,
        userModified: false,
        endpoint: tool.endpoint,
        parameters: tool.parameters,
        responseSchema: tool.responseSchema,
        apiSpecId: apiSpec.id,
      }));

      await prisma.functionTool.createMany({
        data: toolsData,
      });
    }

    res.status(201).json(
      createSuccessResponse(
        {
          id: apiSpec.id,
          name: apiSpec.name,
          description: apiSpec.description,
          baseUrl: apiSpec.baseUrl,
          toolCount: apiSpec.toolCount,
          functionTools: functionTools,
          createdAt: apiSpec.createdAt.toISOString(),
        },
        "API specification created successfully",
      ),
    );
  } catch (error) {
    console.error("Error creating API specification:", error);
    throw error;
  }
}

async function handleListApiSpecifications(
  req: NextApiRequest,
  res: NextApiResponse,
  session: any,
  teamId: string,
) {
  const specs = await prisma.apiSpecification.findMany({
    where: {
      teamId: teamId,
    },
    orderBy: {
      createdAt: "desc",
    },
    include: {
      _count: {
        select: {
          functionTools: true,
        },
      },
    },
  });

  const formattedSpecs = specs.map((spec) => ({
    id: spec.id,
    name: spec.name,
    description: spec.description,
    baseUrl: spec.baseUrl,
    version: spec.version,
    toolCount: spec._count.functionTools,
    enabled: spec.enabled,
    status: spec.status,
    createdAt: spec.createdAt.toISOString(),
    updatedAt: spec.updatedAt.toISOString(),
  }));

  res.status(200).json(
    createSuccessResponse({
      specifications: formattedSpecs,
      total: specs.length,
    }),
  );
}

export default withErrorHandling(apiSpecificationsHandler);
