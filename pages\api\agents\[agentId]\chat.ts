/**
 * API Route: Chat with AI Agent
 * This implements the conversational interface for API agents
 */
import { NextApiRequest, NextApiResponse } from "next";

import { getServerSession } from "next-auth/next";

import {
  APICallPlan,
  ConversationContext,
  ExecutionResult,
  FunctionTool,
  createRestGPTEngine,
} from "../../../../lib/restgpt-engine";
import { authOptions } from "../../auth/[...nextauth]";

interface ChatRequest {
  message: string;
  sessionId?: string;
  context?: Record<string, any>;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const { agentId } = req.query;
  if (!agentId || typeof agentId !== "string") {
    return res.status(400).json({ error: "Invalid agent ID" });
  }

  // Handle initial data fetch for the chat UI
  if (req.method === "GET") {
    // TODO: Replace with real DB fetches
    const mockAgent = {
      id: agentId,
      name: "Pet Store Assistant",
      description: "Helps interact with the Pet Store API.",
      specId: "spec_1",
      specName: "Pet Store API",
      status: "active" as const,
      toolCount: 1,
      conversationCount: 0,
      configuration: {
        model: "gpt-4o-mini",
        temperature: 0.7,
      },
    };

    const emptyMessages: any[] = [];

    const conversationStub = {
      id: `conv_${Date.now()}`,
      title: "New Conversation",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return res.status(200).json({
      success: true,
      data: {
        agent: mockAgent,
        messages: emptyMessages,
        conversation: conversationStub,
      },
    });
  }

  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { message, sessionId, context }: ChatRequest = req.body;

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    // Fallback: if OpenAI API key is not set, return a mock streaming response
    if (!process.env.OPENAI_API_KEY) {
      const replyText = `Please set the correct OPENAI_API_KEY in the .env file`;

      res.setHeader("Content-Type", "text/event-stream");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");

      // Initial assistant content
      res.write(
        `data: ${JSON.stringify({ type: "content", content: replyText })}\n\n`,
      );
      // End of stream indicator (optional for client)
      res.write("data: [DONE]\n\n");
      res.end();
      return;
    }

    // TODO: Get agent configuration from database
    // For now, use mock agent data
    const mockAgent = {
      id: agentId,
      name: "Pet Store Assistant",
      specId: "spec_1",
      systemPrompt: "You are a helpful assistant for the Pet Store API.",
      configuration: {
        model: "gpt-4o-mini",
        temperature: 0.7,
      },
      availableTools: [
        {
          id: "tool_1",
          name: "getPetById",
          method: "GET",
          description: "Find pet by ID",
          enabled: true,
        },
      ],
    } as const;

    // TODO: Get OpenAPI spec from database
    const mockSpec = {
      openapi: "3.0.2",
      info: { title: "Pet Store API", version: "1.0.0" },
      paths: {
        "/pet/{petId}": {
          get: {
            operationId: "getPetById",
            summary: "Find pet by ID",
            parameters: [
              {
                name: "petId",
                in: "path",
                required: true,
                schema: { type: "integer" },
              },
            ],
            responses: {
              "200": {
                description: "successful operation",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        id: { type: "integer" },
                        name: { type: "string" },
                        status: { type: "string" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    };

    // Initialize RestGPT engine
    const engine = createRestGPTEngine({
      openaiApiKey: process.env.OPENAI_API_KEY!,
      enableLogging: true,
    });

    const initResult = await engine.initialize(
      mockSpec,
      "https://petstore3.swagger.io/api/v3",
    );

    if (initResult.success) {
      // Apply the agent's tool configuration – disable any tools not enabled by the agent
      initResult.tools.forEach((tool: FunctionTool) => {
        const matching = mockAgent.availableTools.find(
          (t) => t.name === tool.name,
        );
        if (matching && !matching.enabled) {
          engine.toggleTool(tool.id, false);
        }
        // If tool isn't listed in agent config, disable it by default
        if (!matching) {
          engine.toggleTool(tool.id, false);
        }
      });
    }

    if (!initResult.success) {
      return res.status(500).json({
        error: "Failed to initialize agent engine",
      });
    }

    // Build conversation context including the agent's system prompt
    const conversationContext: ConversationContext = {
      sessionId: sessionId || `session_${Date.now()}`,
      userId:
        (session.user as { id?: string | null }).id ??
        session.user.email ??
        "anonymous",
      agentId,
      messageHistory: [
        {
          role: "system",
          content: mockAgent.systemPrompt,
          timestamp: new Date(),
        },
        {
          role: "user",
          content: message,
          timestamp: new Date(),
        },
      ],
      executionHistory: [],
    };

    // Process the user request
    const result = await engine.processRequest(message, conversationContext);

    if (!result.success) {
      return res.status(500).json({
        error: "Failed to process request",
        message: result.error,
      });
    }

    // TODO: Save conversation to database

    res.status(200).json({
      success: true,
      data: {
        agentId,
        sessionId: conversationContext.sessionId,
        response: result.response,
        executionDetails: result.executionHistory.map(
          (exec: { plan: APICallPlan; result: ExecutionResult }) => ({
            tool: exec.plan.toolId,
            success: exec.result.success,
            responseTime: exec.result.responseTime,
          }),
        ),
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Chat error:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
