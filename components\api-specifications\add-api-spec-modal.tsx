import { useRouter } from "next/router";
import { useState } from "react";

import { useTeam } from "@/context/team-context";
import { ApiAuthType } from "@prisma/client";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

import { ApiSpecUpload } from "./api-spec-upload";
import { ApiSpecUrlInput } from "./api-spec-url-input";
import { ApiSpecTextInput } from "./api-spec-text-input";

interface AddApiSpecModalProps {
  children: React.ReactNode;
}

export function AddApiSpecModal({ children }: AddApiSpecModalProps) {
  const router = useRouter();
  const teamInfo = useTeam();
  const [isOpen, setIsOpen] = useState(false);
  const [uploading, setUploading] = useState(false);

  const teamId = teamInfo?.currentTeam?.id;

  const handleSuccess = (apiSpecification: any) => {
    setIsOpen(false);
    toast.success("API specification created successfully!");
    router.push(`/specs/${apiSpecification.id}`);
  };

  const handleError = (error: string) => {
    toast.error(error);
  };

  if (!teamId) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add API Specification</DialogTitle>
          <DialogDescription>
            Upload an OpenAPI/Swagger specification file, provide a URL, or paste the specification directly.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="upload" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload">Upload File</TabsTrigger>
            <TabsTrigger value="url">From URL</TabsTrigger>
            <TabsTrigger value="text">Paste Text</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Upload a JSON or YAML file containing your OpenAPI 3.x or Swagger 2.x specification.
            </div>
            <ApiSpecUpload
              teamId={teamId}
              onSuccess={handleSuccess}
              onError={handleError}
              uploading={uploading}
              setUploading={setUploading}
            />
          </TabsContent>

          <TabsContent value="url" className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Provide a URL to your OpenAPI/Swagger specification. We'll fetch and parse it for you.
            </div>
            <ApiSpecUrlInput
              teamId={teamId}
              onSuccess={handleSuccess}
              onError={handleError}
              uploading={uploading}
              setUploading={setUploading}
            />
          </TabsContent>

          <TabsContent value="text" className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Paste your OpenAPI/Swagger specification directly as JSON or YAML text.
            </div>
            <ApiSpecTextInput
              teamId={teamId}
              onSuccess={handleSuccess}
              onError={handleError}
              uploading={uploading}
              setUploading={setUploading}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
