/**
 * API Route: List API specifications for a user/team
 * Replaces Papermark's document listing functionality
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { teamId, page = '1', limit = '10' } = req.query;

    // TODO: Replace with actual database query
    // For now, return mock data
    const mockSpecs = [
      {
        id: 'spec_1',
        name: 'Pet Store API',
        description: 'A sample Pet Store Server based on the OpenAPI 3.0 specification',
        baseUrl: 'https://petstore3.swagger.io/api/v3',
        toolCount: 18,
        enabled: true,
        createdAt: '2024-06-20T10:00:00Z',
        updatedAt: '2024-06-20T10:00:00Z',
        createdBy: session.user.id,
        team: teamId || null
      },
      {
        id: 'spec_2',
        name: 'JSON Placeholder API',
        description: 'Free fake API for testing and prototyping',
        baseUrl: 'https://jsonplaceholder.typicode.com',
        toolCount: 12,
        enabled: true,
        createdAt: '2024-06-19T15:30:00Z',
        updatedAt: '2024-06-19T15:30:00Z',
        createdBy: session.user.id,
        team: teamId || null
      }
    ];

    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const paginatedSpecs = mockSpecs.slice(startIndex, endIndex);

    res.status(200).json({
      success: true,
      data: {
        specs: paginatedSpecs,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: mockSpecs.length,
          totalPages: Math.ceil(mockSpecs.length / limitNum)
        }
      }
    });

  } catch (error) {
    console.error('List specs error:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
