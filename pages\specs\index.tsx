import { useRouter } from "next/router";

import { useState, useCallback } from "react";

import { useTeam } from "@/context/team-context";
import { 
  FileCode, 
  Plus, 
  Upload,
  Settings,
  Eye,
  MoreHorizontal,
  Calendar,
  CheckCircle,
  XCircle,
  Bot
} from "lucide-react";
import { toast } from "sonner";
import useS<PERSON> from "swr";

import AppLayout from "@/components/layouts/app";
import { SpecUploadZone } from "@/components/spec-upload-zone";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { fetcher, formatDate } from "@/lib/utils";

interface ApiSpec {
  id: string;
  name: string;
  description: string;
  baseUrl: string;
  toolCount: number;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  team: string | null;
}

interface SpecsResponse {
  success: boolean;
  data: {
    specs: ApiSpec[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export default function SpecsPage() {
  const router = useRouter();
  const teamInfo = useTeam();
  const [showUploadDialog, setShowUploadDialog] = useState(false);

  const { 
    data: specsData, 
    error, 
    mutate: refreshSpecs 
  } = useSWR<SpecsResponse>(
    teamInfo?.currentTeam?.id 
      ? `/api/specs?teamId=${teamInfo.currentTeam.id}`
      : null,
    fetcher,
    {
      refreshInterval: 30000, // Refresh every 30 seconds
    }
  );

  const handleSpecClick = useCallback((specId: string) => {
    router.push(`/specs/${specId}/tools`);
  }, [router]);

  const handleCreateAgent = useCallback((specId: string) => {
    router.push(`/agents/create?specId=${specId}`);
  }, [router]);

  const handleUploadSuccess = useCallback(() => {
    setShowUploadDialog(false);
    refreshSpecs();
    toast.success("API specification uploaded successfully!");
  }, [refreshSpecs]);

  const handleFileUpload = useCallback(async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    if (teamInfo?.currentTeam?.id) {
      formData.append('teamId', teamInfo.currentTeam.id);
    }

    try {
      const response = await fetch('/api/specs/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const result = await response.json();
      if (result.success) {
        handleUploadSuccess();
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload API specification. Please try again.');
    }
  }, [teamInfo?.currentTeam?.id, handleUploadSuccess]);

  const specs = specsData?.data?.specs || [];
  const isLoading = !specsData && !error;

  return (
    <AppLayout>
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">API Specifications</h2>
            <p className="text-muted-foreground">
              Manage your OpenAPI specifications and transform them into AI tools
            </p>
          </div>
          
          <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Upload API Spec
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Upload OpenAPI Specification</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Upload a JSON or YAML file containing your OpenAPI 3.0+ specification.
                </p>
                <SpecUploadZone onUpload={handleFileUpload} />
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total APIs</CardTitle>
              <FileCode className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{specs.length}</div>
              <p className="text-xs text-muted-foreground">
                Uploaded specifications
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active APIs</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {specs.filter(spec => spec.enabled).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Ready for agent creation
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tools</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {specs.reduce((total, spec) => total + spec.toolCount, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Generated function tools
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Specs Table */}
        <Card>
          <CardHeader>
            <CardTitle>API Specifications</CardTitle>
            <CardDescription>
              Your uploaded OpenAPI specifications and their tool generation status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : specs.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <FileCode className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No API specifications yet</h3>
                <p className="text-muted-foreground mb-4">
                  Upload your first OpenAPI specification to get started
                </p>
                <Button 
                  onClick={() => setShowUploadDialog(true)}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  Upload API Spec
                </Button>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Base URL</TableHead>
                    <TableHead>Tools</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {specs.map((spec) => (
                    <TableRow 
                      key={spec.id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSpecClick(spec.id)}
                    >
                      <TableCell>
                        <div>
                          <div className="font-medium">{spec.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {spec.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {spec.baseUrl}
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {spec.toolCount} tools
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {spec.enabled ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                          <span className="text-sm">
                            {spec.enabled ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="h-4 w-4" />
                          {formatDate(spec.createdAt)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleSpecClick(spec.id)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Tools
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleCreateAgent(spec.id)}>
                              <Bot className="h-4 w-4 mr-2" />
                              Create Agent
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleSpecClick(spec.id)}>
                              <Settings className="h-4 w-4 mr-2" />
                              Settings
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
