/**
 * API Route: Create AI Agent from selected tools
 * This implements the "Create Agent" feature from the transformation plan
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { createRestGPTEngine } from '../../../lib/restgpt-engine';

interface CreateAgentRequest {
  name: string;
  description?: string;
  specId: string;
  selectedToolIds: string[];
  systemPrompt?: string;
  configuration?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const {
      name,
      description,
      specId,
      selectedToolIds,
      systemPrompt,
      configuration
    }: CreateAgentRequest = req.body;

    // Validate required fields
    if (!name || !specId || !selectedToolIds || selectedToolIds.length === 0) {
      return res.status(400).json({ 
        error: 'Name, spec ID, and at least one tool selection are required' 
      });
    }

    // TODO: Get actual spec and tools from database
    // For now, simulate agent creation
    
    const agentId = `agent_${Date.now()}`;
    
    // Initialize RestGPT engine for the agent
    const engine = createRestGPTEngine({
      openaiApiKey: process.env.OPENAI_API_KEY!,
      enableLogging: true,
      maxRetries: 3,
      timeout: 30000
    });

    // TODO: Get actual OpenAPI spec and filter tools by selectedToolIds
    // For now, use mock data
    const mockSpec = {
      openapi: '3.0.2',
      info: { title: name, version: '1.0.0' },
      paths: {
        '/pet/{petId}': {
          get: {
            operationId: 'getPetById',
            summary: 'Find pet by ID',
            parameters: [
              {
                name: 'petId',
                in: 'path',
                required: true,
                schema: { type: 'integer' }
              }
            ],
            responses: { '200': { description: 'Success' } }
          }
        }
      }
    };

    const initResult = await engine.initialize(mockSpec, 'https://petstore3.swagger.io/api/v3');
    
    if (!initResult.success) {
      return res.status(400).json({
        error: 'Failed to initialize agent engine',
        details: initResult.errors
      });
    }

    // Create agent record
    const newAgent = {
      id: agentId,
      name,
      description: description || `AI assistant for ${name}`,
      specId,
      selectedToolIds,
      systemPrompt: systemPrompt || generateDefaultSystemPrompt(name, initResult.tools),
      configuration: {
        model: configuration?.model || 'gpt-4o-mini',
        temperature: configuration?.temperature || 0.7,
        maxTokens: configuration?.maxTokens || 1000
      },
      status: 'active',
      availableTools: initResult.tools.filter(tool => 
        selectedToolIds.includes(tool.id)
      ),
      createdAt: new Date().toISOString(),
      createdBy: session.user.id,
      conversationCount: 0,
      lastUsed: null
    };

    // TODO: Save agent to database
    
    res.status(201).json({
      success: true,
      message: 'AI Agent created successfully',
      data: newAgent
    });

  } catch (error) {
    console.error('Agent creation error:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

function generateDefaultSystemPrompt(agentName: string, tools: any[]): string {
  const toolsList = tools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n');
  
  return `You are ${agentName}, an AI assistant that can interact with APIs to help users accomplish their tasks.

Available Tools:
${toolsList}

Your capabilities:
- Understand user requests and break them down into actionable steps
- Use the available API tools to retrieve and manipulate data
- Provide clear, helpful responses based on API results
- Handle errors gracefully and suggest alternatives when needed

Guidelines:
- Always explain what you're doing before making API calls
- Present results in a clear, user-friendly format
- Ask for clarification when requests are ambiguous
- Be helpful, accurate, and efficient in your responses`;
}
