/**
 * API Route: Create AI Agent from selected tools
 * This implements the "Create Agent" feature from the transformation plan
 */
import { NextApiRequest, NextApiResponse } from "next";

import { getServerSession } from "next-auth/next";

import prisma from "../../../lib/prisma";
import { createRestGPTEngine } from "../../../lib/restgpt-engine";
import { authOptions } from "../auth/[...nextauth]";

interface CreateAgentRequest {
  name: string;
  description?: string;
  specId: string;
  selectedToolIds: string[];
  systemPrompt?: string;
  configuration?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    const {
      name,
      description,
      specId,
      selectedToolIds,
      systemPrompt,
      configuration,
    }: CreateAgentRequest = req.body;

    // Validate required fields
    if (!name || !specId || !selectedToolIds || selectedToolIds.length === 0) {
      return res.status(400).json({
        error: "Name, spec ID, and at least one tool selection are required",
      });
    }

    // Get API specification and tools from database
    const apiSpec = await prisma.apiSpecification.findFirst({
      where: {
        id: specId,
        OR: [
          { ownerId: session.user.id },
          { team: { users: { some: { userId: session.user.id } } } },
        ],
      },
      include: {
        tools: {
          where: {
            id: { in: selectedToolIds },
            enabled: true,
          },
        },
      },
    });

    if (!apiSpec) {
      return res.status(404).json({
        error: "API specification not found",
      });
    }

    if (apiSpec.tools.length === 0) {
      return res.status(400).json({
        error: "No valid tools found for the selected tool IDs",
      });
    }

    // Initialize RestGPT engine for the agent
    const engine = createRestGPTEngine({
      openaiApiKey: process.env.OPENAI_API_KEY!,
      enableLogging: true,
      maxRetries: 3,
      timeout: 30000,
    });

    const initResult = await engine.initialize(
      apiSpec.openApiSpec,
      apiSpec.baseUrl,
    );

    if (!initResult.success) {
      return res.status(400).json({
        error: "Failed to initialize agent engine",
        details: initResult.errors,
      });
    }

    // Create agent in database
    const newAgent = await prisma.agent.create({
      data: {
        name,
        description: description || `AI assistant for ${name}`,
        systemPrompt:
          systemPrompt || generateDefaultSystemPrompt(name, apiSpec.tools),
        status: "ACTIVE",
        modelConfig: {
          model: configuration?.model || "gpt-4o-mini",
          temperature: configuration?.temperature || 0.7,
          maxTokens: configuration?.maxTokens || 1000,
        },
        ownerId: session.user.id,
        teamId: apiSpec.teamId,
        apiSpecId: specId,
      },
    });

    // Create agent-tool relationships
    const agentToolsData = selectedToolIds.map((toolId) => ({
      agentId: newAgent.id,
      toolId,
      enabled: true,
    }));

    await prisma.agentTool.createMany({
      data: agentToolsData,
    });

    // Get the created agent with tools for response
    const agentWithTools = await prisma.agent.findUnique({
      where: { id: newAgent.id },
      include: {
        tools: {
          include: {
            tool: true,
          },
        },
        apiSpec: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      message: "AI Agent created successfully",
      data: {
        id: agentWithTools!.id,
        name: agentWithTools!.name,
        description: agentWithTools!.description,
        specId: agentWithTools!.apiSpecId,
        specName: agentWithTools!.apiSpec.name,
        systemPrompt: agentWithTools!.systemPrompt,
        configuration: agentWithTools!.modelConfig,
        status: agentWithTools!.status.toLowerCase(),
        availableTools: agentWithTools!.tools.map((at) => ({
          id: at.tool.id,
          name: at.tool.name,
          description: at.tool.description,
          enabled: at.enabled,
        })),
        createdAt: agentWithTools!.createdAt.toISOString(),
        createdBy: agentWithTools!.ownerId,
        conversationCount: agentWithTools!.conversationCount,
        lastUsed: agentWithTools!.lastUsed?.toISOString() || null,
      },
    });
  } catch (error) {
    console.error("Agent creation error:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}

function generateDefaultSystemPrompt(agentName: string, tools: any[]): string {
  const toolsList = tools
    .map((tool) => `- ${tool.name}: ${tool.description}`)
    .join("\n");

  return `You are ${agentName}, an AI assistant that can interact with APIs to help users accomplish their tasks.

Available Tools:
${toolsList}

Your capabilities:
- Understand user requests and break them down into actionable steps
- Use the available API tools to retrieve and manipulate data
- Provide clear, helpful responses based on API results
- Handle errors gracefully and suggest alternatives when needed

Guidelines:
- Always explain what you're doing before making API calls
- Present results in a clear, user-friendly format
- Ask for clarification when requests are ambiguous
- Be helpful, accurate, and efficient in your responses`;
}
