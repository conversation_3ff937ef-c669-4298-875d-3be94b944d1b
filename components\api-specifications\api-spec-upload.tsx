import { useState } from "react";

import { useDropzone } from "react-dropzone";
import { CloudUploadIcon, FileTextIcon, XIcon } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";


import { getSupportedContentType } from "@/lib/utils/get-content-type";
import { parseApiSpecification } from "@/lib/api-specifications/parse-api-spec";

interface ApiSpecUploadProps {
  teamId: string;
  onSuccess: (apiSpecification: any) => void;
  onError: (error: string) => void;
  uploading: boolean;
  setUploading: (uploading: boolean) => void;
}

export function ApiSpecUpload({
  teamId,
  onSuccess,
  onError,
  uploading,
  setUploading,
}: ApiSpecUploadProps) {
  const [currentFile, setCurrentFile] = useState<File | null>(null);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [baseUrl, setBaseUrl] = useState("");
  const [apiVersion, setApiVersion] = useState("");
  const [authType, setAuthType] = useState<string>("");

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      "application/json": [".json"],
      "application/x-yaml": [".yaml", ".yml"],
      "text/yaml": [".yaml", ".yml"],
      "text/x-yaml": [".yaml", ".yml"],
    },
    multiple: false,
    onDropAccepted: (acceptedFiles) => {
      const file = acceptedFiles[0];
      setCurrentFile(file);
      if (!name) {
        setName(file.name.replace(/\.(json|yaml|yml)$/, ""));
      }
    },
    onDropRejected: (rejectedFiles) => {
      const rejection = rejectedFiles[0];
      if (rejection.errors[0]?.code === "file-invalid-type") {
        onError("Please upload a JSON or YAML file");
      } else {
        onError("File upload failed");
      }
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentFile) {
      onError("Please select a file");
      return;
    }

    if (!name.trim()) {
      onError("Please provide a name for the API specification");
      return;
    }

    setUploading(true);

    try {
      // Read and parse the file content
      const fileContent = await currentFile.text();
      const parsedSpec = await parseApiSpecification(fileContent, currentFile.type);

      // Skip file storage for API specifications - work directly with parsed content
      // Create API specification directly with parsed schema
      const apiSpecResponse = await fetch(`/api/teams/${teamId}/api-specifications`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: name.trim(),
          description: description.trim() || undefined,
          baseUrl: baseUrl.trim() || parsedSpec.baseUrl || undefined,
          apiVersion: apiVersion.trim() || parsedSpec.version || undefined,
          authType: authType || parsedSpec.authType || undefined,
          authConfig: parsedSpec.authConfig || undefined,
          parsedSchema: parsedSpec.schema || undefined,
          functionTools: parsedSpec.functionTools || [],
        }),
      });

      if (!apiSpecResponse.ok) {
        throw new Error("Failed to create API specification");
      }

      const apiSpecification = await apiSpecResponse.json();
      onSuccess(apiSpecification);
    } catch (error) {
      console.error("Error creating API specification:", error);
      onError(error instanceof Error ? error.message : "Failed to create API specification");
    } finally {
      setUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* File Upload */}
      <div className="space-y-2">
        <Label>API Specification File</Label>
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
            isDragActive
              ? "border-blue-500 bg-blue-50 dark:bg-blue-950"
              : "border-gray-300 hover:border-gray-400 dark:border-gray-600"
          }`}
        >
          <input {...getInputProps()} />
          {currentFile ? (
            <div className="flex items-center justify-center space-x-2">
              <FileTextIcon className="h-8 w-8 text-blue-600" />
              <div className="text-left">
                <p className="font-medium">{currentFile.name}</p>
                <p className="text-sm text-muted-foreground">
                  {(currentFile.size / 1024).toFixed(1)} KB
                </p>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentFile(null);
                }}
              >
                <XIcon className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              <CloudUploadIcon className="h-12 w-12 mx-auto text-gray-400" />
              <div>
                <p className="text-lg font-medium">
                  {isDragActive ? "Drop the file here" : "Upload API specification"}
                </p>
                <p className="text-sm text-muted-foreground">
                  Drag and drop a JSON or YAML file, or click to browse
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Metadata */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Name *</Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="My API v1"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="apiVersion">API Version</Label>
          <Input
            id="apiVersion"
            value={apiVersion}
            onChange={(e) => setApiVersion(e.target.value)}
            placeholder="1.0.0"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Brief description of your API"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="baseUrl">Base URL</Label>
          <Input
            id="baseUrl"
            value={baseUrl}
            onChange={(e) => setBaseUrl(e.target.value)}
            placeholder="https://api.example.com/v1"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="authType">Authentication Type</Label>
          <Select value={authType} onValueChange={setAuthType}>
            <SelectTrigger>
              <SelectValue placeholder="Select auth type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="NONE">None</SelectItem>
              <SelectItem value="API_KEY">API Key</SelectItem>
              <SelectItem value="BEARER_TOKEN">Bearer Token</SelectItem>
              <SelectItem value="BASIC_AUTH">Basic Auth</SelectItem>
              <SelectItem value="OAUTH2">OAuth 2.0</SelectItem>
              <SelectItem value="CUSTOM">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex justify-end">
        <Button type="submit" disabled={uploading || !currentFile}>
          {uploading ? "Creating..." : "Create API Specification"}
        </Button>
      </div>
    </form>
  );
}
