import { useRouter } from "next/router";

import { useState, useC<PERSON>back, useEffect } from "react";

import { useTeam } from "@/context/team-context";
import { 
  ArrowLeft,
  ArrowRight,
  Bot,
  Check,
  CheckCircle,
  Code,
  Settings,
  Sparkles,
  Zap
} from "lucide-react";
import { toast } from "sonner";
import useS<PERSON> from "swr";

import AppLayout from "@/components/layouts/app";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";

import { fetcher } from "@/lib/utils";

interface Tool {
  id: string;
  name: string;
  description: string;
  method: string;
  path: string;
  enabled: boolean;
}

interface ApiSpec {
  id: string;
  name: string;
  description: string;
  baseUrl: string;
  toolCount: number;
  enabled: boolean;
  tools?: Tool[];
}

interface AgentConfiguration {
  name: string;
  description: string;
  specId: string;
  selectedTools: string[];
  model: string;
  temperature: number;
  systemPrompt: string;
}

const STEPS = [
  { id: 1, title: "Select API", description: "Choose the API specification" },
  { id: 2, title: "Configure Tools", description: "Select function tools" },
  { id: 3, title: "Agent Settings", description: "Configure your AI agent" },
  { id: 4, title: "Review", description: "Review and create" },
];

const AI_MODELS = [
  { value: "gpt-4o-mini", label: "GPT-4O Mini (Recommended)" },
  { value: "gpt-4", label: "GPT-4" },
  { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo" },
];

export default function CreateAgentPage() {
  const router = useRouter();
  const { specId: preselectedSpecId } = router.query;
  const teamInfo = useTeam();
  const [currentStep, setCurrentStep] = useState(1);
  const [isCreating, setIsCreating] = useState(false);

  const [config, setConfig] = useState<AgentConfiguration>({
    name: "",
    description: "",
    specId: "",
    selectedTools: [],
    model: "gpt-4o-mini",
    temperature: 0.7,
    systemPrompt: "You are a helpful AI assistant that can interact with APIs to help users accomplish their tasks. Use the available tools when needed and provide clear explanations of your actions.",
  });

  // Fetch available API specs
  const { data: specsData } = useSWR(
    teamInfo?.currentTeam?.id 
      ? `/api/specs?teamId=${teamInfo.currentTeam.id}`
      : null,
    fetcher
  );

  // Fetch tools for selected spec
  const { data: toolsData } = useSWR(
    config.specId && teamInfo?.currentTeam?.id 
      ? `/api/tools/${config.specId}?teamId=${teamInfo.currentTeam.id}`
      : null,
    fetcher
  );

  // Auto-select spec if coming from spec page
  useEffect(() => {
    if (preselectedSpecId && typeof preselectedSpecId === 'string') {
      setConfig(prev => ({ ...prev, specId: preselectedSpecId }));
      setCurrentStep(2); // Skip to tools selection
    }
  }, [preselectedSpecId]);

  const specs = (specsData as any)?.data?.specs || [];
  const tools = (toolsData as any)?.data?.tools || [];
  const selectedSpec = specs.find((spec: any) => spec.id === config.specId);

  const handleBack = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.push('/agents');
    }
  }, [currentStep, router]);

  const handleNext = useCallback(() => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep]);

  const handleSpecSelect = useCallback((specId: string) => {
    setConfig(prev => ({ 
      ...prev, 
      specId,
      selectedTools: [] // Reset selected tools when spec changes
    }));
  }, []);

  const handleToolToggle = useCallback((toolId: string, checked: boolean) => {
    setConfig(prev => ({
      ...prev,
      selectedTools: checked 
        ? [...prev.selectedTools, toolId]
        : prev.selectedTools.filter(id => id !== toolId)
    }));
  }, []);

  const handleConfigChange = useCallback((updates: Partial<AgentConfiguration>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  }, []);

  const handleCreateAgent = useCallback(async () => {
    if (!config.name || !config.specId || config.selectedTools.length === 0) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsCreating(true);
    try {
      const response = await fetch('/api/agents/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...config,
          teamId: teamInfo?.currentTeam?.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create agent');
      }

      const result = await response.json();
      if (result.success) {
        toast.success("Agent created successfully!");
        router.push('/agents');
      } else {
        throw new Error(result.error || 'Failed to create agent');
      }
    } catch (error) {
      console.error('Create agent error:', error);
      toast.error('Failed to create agent. Please try again.');
    } finally {
      setIsCreating(false);
    }
  }, [config, teamInfo?.currentTeam?.id, router]);

  const canProceedFromStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!config.specId;
      case 2:
        return config.selectedTools.length > 0;
      case 3:
        return !!config.name && !!config.description;
      default:
        return true;
    }
  };

  const progress = (currentStep / STEPS.length) * 100;

  return (
    <AppLayout>
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8 max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h2 className="text-3xl font-bold tracking-tight">Create AI Agent</h2>
              <p className="text-muted-foreground">
                Build an AI assistant from your API specifications
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Bot className="h-8 w-8 text-primary" />
            <Sparkles className="h-6 w-6 text-yellow-500" />
          </div>
        </div>

        {/* Progress */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">
              Step {currentStep} of {STEPS.length}
            </span>
            <span className="text-sm text-muted-foreground">
              {Math.round(progress)}% complete
            </span>
          </div>
          <Progress value={progress} className="w-full" />
          
          <div className="flex items-center justify-between">
            {STEPS.map((step, index) => (
              <div 
                key={step.id}
                className={`flex items-center gap-2 ${
                  step.id <= currentStep ? 'text-primary' : 'text-muted-foreground'
                }`}
              >
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  step.id < currentStep 
                    ? 'bg-primary border-primary text-primary-foreground'
                    : step.id === currentStep
                    ? 'border-primary text-primary'
                    : 'border-muted text-muted-foreground'
                }`}>
                  {step.id < currentStep ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    step.id
                  )}
                </div>
                <div className="hidden md:block">
                  <div className="font-medium text-sm">{step.title}</div>
                  <div className="text-xs text-muted-foreground">{step.description}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <Card>
          <CardContent className="p-6">
            {/* Step 1: Select API */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Select API Specification</h3>
                  <p className="text-muted-foreground">
                    Choose the API specification that your agent will use
                  </p>
                </div>
                
                {specs.length === 0 ? (
                  <div className="text-center py-8">
                    <Code className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h4 className="text-lg font-medium mb-2">No API specifications found</h4>
                    <p className="text-muted-foreground mb-4">
                      You need to upload an API specification first
                    </p>
                    <Button onClick={() => router.push('/specs')}>
                      Upload API Spec
                    </Button>
                  </div>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2">
                    {specs.map((spec: any) => (
                      <Card 
                        key={spec.id}
                        className={`cursor-pointer transition-colors ${
                          config.specId === spec.id 
                            ? 'ring-2 ring-primary' 
                            : 'hover:bg-muted/50'
                        }`}
                        onClick={() => handleSpecSelect(spec.id)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-2">
                              <Code className="h-5 w-5" />
                              <CardTitle className="text-base">{spec.name}</CardTitle>
                            </div>
                            {config.specId === spec.id && (
                              <CheckCircle className="h-5 w-5 text-primary" />
                            )}
                          </div>
                          <CardDescription>{spec.description}</CardDescription>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex items-center justify-between text-sm">
                            <Badge variant="secondary">
                              {spec.toolCount} tools
                            </Badge>
                            <span className="text-muted-foreground">
                              {spec.enabled ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Step 2: Configure Tools */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Select Function Tools</h3>
                  <p className="text-muted-foreground">
                    Choose which API endpoints your agent can access
                  </p>
                </div>
                
                {selectedSpec && (
                  <Card>
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-2">
                        <Code className="h-5 w-5" />
                        <CardTitle className="text-base">{selectedSpec.name}</CardTitle>
                      </div>
                      <CardDescription>{selectedSpec.description}</CardDescription>
                    </CardHeader>
                  </Card>
                )}
                
                <div className="space-y-4">
                  {tools.length === 0 ? (
                    <div className="text-center py-8">
                      <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h4 className="text-lg font-medium mb-2">No tools available</h4>
                      <p className="text-muted-foreground">
                        No function tools have been generated for this API specification
                      </p>
                    </div>
                  ) : (
                    <>
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">
                          Available Tools ({tools.filter((t: any) => t.enabled).length} enabled)
                        </Label>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const enabledTools = tools.filter((t: any) => t.enabled).map((t: any) => t.id);
                            setConfig(prev => ({ ...prev, selectedTools: enabledTools }));
                          }}
                        >
                          Select All Enabled
                        </Button>
                      </div>
                      
                      {tools.map((tool: any) => (
                        <div 
                          key={tool.id}
                          className={`flex items-start gap-3 p-4 border rounded-lg ${
                            !tool.enabled ? 'opacity-50' : ''
                          }`}
                        >
                          <Checkbox
                            checked={config.selectedTools.includes(tool.id)}
                            disabled={!tool.enabled}
                            onCheckedChange={(checked) => 
                              handleToolToggle(tool.id, checked as boolean)
                            }
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium">{tool.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {tool.method.toUpperCase()}
                              </Badge>
                              {!tool.enabled && (
                                <Badge variant="secondary" className="text-xs">
                                  Disabled
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mb-1">
                              {tool.description}
                            </p>
                            <p className="font-mono text-xs text-muted-foreground">
                              {tool.path}
                            </p>
                          </div>
                        </div>
                      ))}
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Step 3: Agent Settings */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Configure Your Agent</h3>
                  <p className="text-muted-foreground">
                    Set up your AI agent's personality and behavior
                  </p>
                </div>
                
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="agent-name">Agent Name *</Label>
                      <Input
                        id="agent-name"
                        value={config.name}
                        onChange={(e) => handleConfigChange({ name: e.target.value })}
                        placeholder="e.g., API Assistant"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="agent-description">Description *</Label>
                      <Textarea
                        id="agent-description"
                        value={config.description}
                        onChange={(e) => handleConfigChange({ description: e.target.value })}
                        placeholder="Describe what your agent does..."
                        rows={3}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="ai-model">AI Model</Label>
                      <Select
                        value={config.model}
                        onValueChange={(value) => handleConfigChange({ model: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.value} value={model.value}>
                              {model.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="temperature">
                        Creativity Level: {config.temperature}
                      </Label>
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={config.temperature}
                        onChange={(e) => handleConfigChange({ temperature: parseFloat(e.target.value) })}
                        className="w-full mt-2"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground mt-1">
                        <span>Focused</span>
                        <span>Creative</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="system-prompt">System Prompt</Label>
                    <Textarea
                      id="system-prompt"
                      value={config.systemPrompt}
                      onChange={(e) => handleConfigChange({ systemPrompt: e.target.value })}
                      rows={8}
                      className="resize-none"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      This prompt defines your agent's personality and behavior
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Review */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Review & Create</h3>
                  <p className="text-muted-foreground">
                    Review your agent configuration before creating
                  </p>
                </div>
                
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Agent Details</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <Label className="text-sm font-medium">Name</Label>
                        <p className="text-sm text-muted-foreground">{config.name}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Description</Label>
                        <p className="text-sm text-muted-foreground">{config.description}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">AI Model</Label>
                        <p className="text-sm text-muted-foreground">{config.model}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Temperature</Label>
                        <p className="text-sm text-muted-foreground">{config.temperature}</p>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Selected Tools</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 mb-3">
                          <Code className="h-4 w-4" />
                          <span className="font-medium">{selectedSpec?.name}</span>
                        </div>
                        <Badge variant="secondary">
                          {config.selectedTools.length} tools selected
                        </Badge>
                        <div className="max-h-32 overflow-y-auto space-y-1">
                          {tools
                            .filter((tool: any) => config.selectedTools.includes(tool.id))
                            .map((tool: any) => (
                              <div key={tool.id} className="text-sm flex items-center gap-2">
                                <CheckCircle className="h-3 w-3 text-green-500" />
                                {tool.name}
                              </div>
                            ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <Button 
            variant="outline" 
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {currentStep === 1 ? 'Cancel' : 'Previous'}
          </Button>
          
          {currentStep < STEPS.length ? (
            <Button 
              onClick={handleNext}
              disabled={!canProceedFromStep(currentStep)}
              className="flex items-center gap-2"
            >
              Next
              <ArrowRight className="h-4 w-4" />
            </Button>
          ) : (
            <Button 
              onClick={handleCreateAgent}
              disabled={isCreating || !canProceedFromStep(currentStep)}
              className="flex items-center gap-2"
            >
              {isCreating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4" />
                  Create Agent
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </AppLayout>
  );
}
