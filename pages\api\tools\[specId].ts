/**
 * API Route: Manage function tools for a specific API specification
 * This is the "API Tool Editor" feature from the transformation plan
 */
import { NextApiRequest, NextApiResponse } from "next";

import { getServerSession } from "next-auth/next";

import prisma from "@/lib/prisma";
import { createRestGPTEngine } from "@/lib/restgpt-engine";

import { authOptions } from "../auth/[...nextauth]";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    const { specId } = req.query;
    if (!specId || typeof specId !== "string") {
      return res.status(400).json({ error: "Invalid specification ID" });
    }

    switch (req.method) {
      case "GET":
        return handleGetTools(req, res, specId, session);
      case "PUT":
        return handleUpdateTool(req, res, specId, session);
      case "POST":
        return handleRegenerateTools(req, res, specId, session);
      default:
        return res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error) {
    console.error("Tools management error:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}

async function handleGetTools(
  req: NextApiRequest,
  res: NextApiResponse,
  specId: string,
  session: any,
) {
  // Check if user has access to this API specification
  const apiSpec = await prisma.apiSpecification.findFirst({
    where: {
      id: specId,
      OR: [
        { ownerId: session.user.id },
        { team: { users: { some: { userId: session.user.id } } } },
      ],
    },
    include: {
      tools: {
        orderBy: { name: "asc" },
      },
    },
  });

  if (!apiSpec) {
    return res.status(404).json({
      error: "API specification not found",
    });
  }

  // Format tools for response
  const formattedTools = apiSpec.tools.map((tool) => ({
    id: tool.id,
    specId: tool.apiSpecId,
    name: tool.name,
    description: tool.description,
    enabled: tool.enabled,
    userModified: tool.userModified,
    endpoint: tool.endpoint,
    parameters: tool.parameters,
    responseSchema: tool.responseSchema,
    createdAt: tool.createdAt,
    updatedAt: tool.updatedAt,
  }));

  res.status(200).json({
    success: true,
    data: {
      specId,
      tools: formattedTools,
      totalTools: formattedTools.length,
      enabledTools: formattedTools.filter((t) => t.enabled).length,
    },
  });
}

async function handleUpdateTool(
  req: NextApiRequest,
  res: NextApiResponse,
  specId: string,
  session: any,
) {
  const { toolId, name, description, enabled, parameters } = req.body;

  if (!toolId) {
    return res.status(400).json({ error: "Tool ID is required" });
  }

  // Check if user has access to this tool's API specification
  const tool = await prisma.functionTool.findFirst({
    where: {
      id: toolId,
      apiSpecId: specId,
      apiSpec: {
        OR: [
          { ownerId: session.user.id },
          { team: { users: { some: { userId: session.user.id } } } },
        ],
      },
    },
  });

  if (!tool) {
    return res.status(404).json({
      error: "Tool not found",
    });
  }

  // Update the tool
  const updatedTool = await prisma.functionTool.update({
    where: { id: toolId },
    data: {
      ...(name && { name }),
      ...(description !== undefined && { description }),
      ...(enabled !== undefined && { enabled }),
      ...(parameters && { parameters }),
      userModified: true,
    },
  });

  res.status(200).json({
    success: true,
    message: "Tool updated successfully",
    data: {
      id: updatedTool.id,
      specId: updatedTool.apiSpecId,
      name: updatedTool.name,
      description: updatedTool.description,
      enabled: updatedTool.enabled,
      parameters: updatedTool.parameters,
      userModified: updatedTool.userModified,
      updatedAt: updatedTool.updatedAt,
    },
  });
}

async function handleRegenerateTools(
  req: NextApiRequest,
  res: NextApiResponse,
  specId: string,
  session: any,
) {
  try {
    // Get OpenAPI spec from database
    const apiSpec = await prisma.apiSpecification.findFirst({
      where: {
        id: specId,
        OR: [
          { ownerId: session.user.id },
          { team: { users: { some: { userId: session.user.id } } } },
        ],
      },
    });

    if (!apiSpec) {
      return res.status(404).json({
        error: "API specification not found",
      });
    }

    const engine = createRestGPTEngine({
      openaiApiKey: process.env.OPENAI_API_KEY!,
      enableLogging: true,
    });

    const result = await engine.initialize(
      apiSpec.openApiSpec,
      apiSpec.baseUrl,
    );

    if (!result.success) {
      return res.status(400).json({
        error: "Failed to regenerate tools",
        details: result.errors,
      });
    }

    // Delete existing tools and create new ones
    await prisma.functionTool.deleteMany({
      where: { apiSpecId: specId },
    });

    const toolsData = result.tools.map((tool: any) => ({
      name: tool.name,
      description: tool.description,
      enabled: true,
      userModified: false,
      endpoint: tool.endpoint,
      parameters: tool.parameters,
      responseSchema: tool.responseSchema,
      apiSpecId: specId,
    }));

    await prisma.functionTool.createMany({
      data: toolsData,
    });

    // Update tool count in API spec
    await prisma.apiSpecification.update({
      where: { id: specId },
      data: { toolCount: result.tools.length },
    });

    res.status(200).json({
      success: true,
      message: "Tools regenerated successfully",
      data: {
        specId,
        newTools: result.tools,
        toolCount: result.tools.length,
      },
    });
  } catch (error) {
    console.error("Tool regeneration error:", error);
    res.status(500).json({
      error: "Failed to regenerate tools",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
