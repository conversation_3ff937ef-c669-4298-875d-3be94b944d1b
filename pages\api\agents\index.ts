/**
 * API Route: List AI Agents for a user/team
 * Manages the collection of created agents
 */
import { NextApiRequest, NextApiResponse } from "next";

import { getServerSession } from "next-auth/next";

import prisma from "../../../lib/prisma";
import { authOptions } from "../auth/[...nextauth]";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    const { teamId, page = "1", limit = "10" } = req.query;

    // TODO: Replace with actual database query
    // For now, return mock agents
    const mockAgents = [
      {
        id: "agent_1",
        name: "Pet Store Assistant",
        description: "AI assistant for managing pet store operations",
        specId: "spec_1",
        specName: "Pet Store API",
        status: "active",
        toolCount: 3,
        conversationCount: 15,
        lastUsed: "2024-06-20T14:30:00Z",
        createdAt: "2024-06-20T10:00:00Z",
        createdBy: session.user.id,
        configuration: {
          model: "gpt-4o-mini",
          temperature: 0.7,
        },
      },
      {
        id: "agent_2",
        name: "JSON Placeholder Helper",
        description:
          "Assistant for testing and prototyping with JSONPlaceholder",
        specId: "spec_2",
        specName: "JSON Placeholder API",
        status: "active",
        toolCount: 5,
        conversationCount: 8,
        lastUsed: "2024-06-19T16:45:00Z",
        createdAt: "2024-06-19T15:30:00Z",
        createdBy: session.user.id,
        configuration: {
          model: "gpt-4o-mini",
          temperature: 0.5,
        },
      },
      {
        id: "agent_3",
        name: "Weather API Agent",
        description: "Get weather information and forecasts",
        specId: "spec_3",
        specName: "Weather API",
        status: "paused",
        toolCount: 4,
        conversationCount: 3,
        lastUsed: "2024-06-18T09:15:00Z",
        createdAt: "2024-06-18T08:00:00Z",
        createdBy: session.user.id,
        configuration: {
          model: "gpt-4o-mini",
          temperature: 0.3,
        },
      },
    ];

    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const paginatedAgents = mockAgents.slice(startIndex, endIndex);

    // Add summary statistics
    const stats = {
      totalAgents: mockAgents.length,
      activeAgents: mockAgents.filter((a) => a.status === "active").length,
      totalConversations: mockAgents.reduce(
        (sum, a) => sum + a.conversationCount,
        0,
      ),
      totalTools: mockAgents.reduce((sum, a) => sum + a.toolCount, 0),
    };

    res.status(200).json({
      success: true,
      data: {
        agents: paginatedAgents,
        stats,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: mockAgents.length,
          totalPages: Math.ceil(mockAgents.length / limitNum),
        },
      },
    });
  } catch (error) {
    console.error("List agents error:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
