/**
 * RestGPT Engine - API to Agent Conversion System
 * 
 * A TypeScript implementation inspired by RestGPT's Parse -> Plan -> Execute strategy
 * Built for Next.js with OpenAI Agents SDK integration
 */

export { RestGPTEngine } from './engine';
export { APISpecParser } from './parser';
export { TaskPlanner } from './planner';
export { APIExecutor } from './executor';
export * from './types';

// Convenience factory function
export function createRestGPTEngine(config: {
  openaiApiKey: string;
  maxRetries?: number;
  timeout?: number;
  enableLogging?: boolean;
}) {
  return new RestGPTEngine({
    openaiApiKey: config.openaiApiKey,
    maxRetries: config.maxRetries || 3,
    timeout: config.timeout || 30000,
    enableLogging: config.enableLogging || false
  });
}
