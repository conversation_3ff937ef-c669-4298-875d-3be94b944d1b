/**
 * RestGPT Engine - API to Agent Conversion System
 *
 * A TypeScript implementation inspired by RestGPT's Parse -> Plan -> Execute strategy
 * Built for Next.js with OpenAI Agents SDK integration
 */
import { RestGPTEngine } from "./engine";

export { RestGPTEngine } from "./engine";
export { APISpecParser } from "./parser";
export { TaskPlanner } from "./planner";
export { APIExecutor } from "./executor";
export * from "./types";

// Convenience factory function
export function createRestGPTEngine(config: {
  openaiApiKey?: string;
  geminiApiKey?: string;
  maxRetries?: number;
  timeout?: number;
  enableLogging?: boolean;
  baseUrl?: string;
}) {
  // Use Gemini API key if provided, otherwise fall back to OpenAI
  const apiKey = config.geminiApiKey || config.openaiApiKey;
  const baseURL = config.geminiApiKey
    ? "https://generativelanguage.googleapis.com/v1beta/openai/"
    : config.baseUrl;

  if (!apiKey) {
    throw new Error("Either geminiApiKey or openaiApiKey must be provided");
  }

  return new RestGPTEngine({
    openaiApiKey: apiKey,
    maxRetries: config.maxRetries || 3,
    timeout: config.timeout || 30000,
    enableLogging: config.enableLogging || false,
    baseUrl: baseURL,
  });
}
