/**
 * API Specification Parser
 * Parses OpenAPI/Swagger specifications and extracts metadata
 */

import { createRestGPTEngine } from "../restgpt-engine";

export interface ParsedApiSpec {
  schema: any;
  baseUrl?: string;
  version?: string;
  authType?: string;
  authConfig?: any;
  functionTools?: any[];
  title?: string;
  description?: string;
}

/**
 * Parse API specification from file content
 */
export async function parseApiSpecification(
  fileContent: string,
  contentType: string
): Promise<ParsedApiSpec> {
  let spec: any;

  try {
    // Parse JSON or YAML
    if (contentType.includes("json")) {
      spec = JSON.parse(fileContent);
    } else if (contentType.includes("yaml") || contentType.includes("yml")) {
      const yaml = require("yaml");
      spec = yaml.parse(fileContent);
    } else {
      // Try JSON first, then YAML
      try {
        spec = JSON.parse(fileContent);
      } catch {
        const yaml = require("yaml");
        spec = yaml.parse(fileContent);
      }
    }
  } catch (error) {
    throw new Error("Invalid API specification format. Please provide valid JSON or YAML.");
  }

  // Validate basic OpenAPI/Swagger structure
  if (!spec.openapi && !spec.swagger) {
    throw new Error("Invalid API specification. Missing openapi or swagger version field.");
  }

  if (!spec.info) {
    throw new Error("Invalid API specification. Missing info object.");
  }

  if (!spec.paths || Object.keys(spec.paths).length === 0) {
    throw new Error("Invalid API specification. Missing or empty paths object.");
  }

  // Extract metadata
  const result: ParsedApiSpec = {
    schema: spec,
    title: spec.info?.title,
    description: spec.info?.description,
    version: spec.info?.version || spec.openapi || spec.swagger,
  };

  // Extract base URL
  if (spec.servers && spec.servers.length > 0) {
    result.baseUrl = spec.servers[0].url;
  } else if (spec.host) {
    // Swagger 2.0 format
    const scheme = spec.schemes?.[0] || "https";
    const basePath = spec.basePath || "";
    result.baseUrl = `${scheme}://${spec.host}${basePath}`;
  }

  // Extract authentication information
  const authInfo = extractAuthInfo(spec);
  if (authInfo) {
    result.authType = authInfo.type;
    result.authConfig = authInfo.config;
  }

  return result;
}

/**
 * Extract authentication information from OpenAPI spec
 */
function extractAuthInfo(spec: any): { type: string; config: any } | null {
  // OpenAPI 3.x security schemes
  if (spec.components?.securitySchemes) {
    const schemes = spec.components.securitySchemes;
    const firstScheme = Object.values(schemes)[0] as any;
    
    if (firstScheme) {
      switch (firstScheme.type) {
        case "apiKey":
          return {
            type: "API_KEY",
            config: {
              name: firstScheme.name,
              in: firstScheme.in,
            },
          };
        case "http":
          if (firstScheme.scheme === "bearer") {
            return {
              type: "BEARER_TOKEN",
              config: {
                bearerFormat: firstScheme.bearerFormat,
              },
            };
          } else if (firstScheme.scheme === "basic") {
            return {
              type: "BASIC_AUTH",
              config: {},
            };
          }
          break;
        case "oauth2":
          return {
            type: "OAUTH2",
            config: {
              flows: firstScheme.flows,
            },
          };
        case "openIdConnect":
          return {
            type: "OAUTH2",
            config: {
              openIdConnectUrl: firstScheme.openIdConnectUrl,
            },
          };
      }
    }
  }

  // Swagger 2.0 security definitions
  if (spec.securityDefinitions) {
    const schemes = spec.securityDefinitions;
    const firstScheme = Object.values(schemes)[0] as any;
    
    if (firstScheme) {
      switch (firstScheme.type) {
        case "apiKey":
          return {
            type: "API_KEY",
            config: {
              name: firstScheme.name,
              in: firstScheme.in,
            },
          };
        case "basic":
          return {
            type: "BASIC_AUTH",
            config: {},
          };
        case "oauth2":
          return {
            type: "OAUTH2",
            config: {
              flow: firstScheme.flow,
              authorizationUrl: firstScheme.authorizationUrl,
              tokenUrl: firstScheme.tokenUrl,
              scopes: firstScheme.scopes,
            },
          };
      }
    }
  }

  return null;
}

/**
 * Generate function tools from parsed specification
 */
export async function generateFunctionTools(
  parsedSpec: ParsedApiSpec,
  baseUrl?: string
): Promise<any[]> {
  if (!process.env.OPENAI_API_KEY) {
    console.warn("OpenAI API key not found. Skipping function tool generation.");
    return [];
  }

  try {
    const engine = createRestGPTEngine({
      openaiApiKey: process.env.OPENAI_API_KEY,
      enableLogging: true,
    });

    const effectiveBaseUrl = baseUrl || parsedSpec.baseUrl;
    if (!effectiveBaseUrl) {
      throw new Error("Base URL is required for function tool generation");
    }

    const result = await engine.initialize(parsedSpec.schema, effectiveBaseUrl);

    if (!result.success) {
      throw new Error(`Failed to generate function tools: ${result.errors?.join(", ")}`);
    }

    return result.tools || [];
  } catch (error) {
    console.error("Error generating function tools:", error);
    throw error;
  }
}

/**
 * Validate OpenAPI specification
 */
export function validateApiSpecification(spec: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check version
  if (!spec.openapi && !spec.swagger) {
    errors.push("Missing openapi or swagger version field");
  }

  // Check info object
  if (!spec.info) {
    errors.push("Missing info object");
  } else {
    if (!spec.info.title) {
      errors.push("Missing info.title");
    }
    if (!spec.info.version) {
      errors.push("Missing info.version");
    }
  }

  // Check paths
  if (!spec.paths || typeof spec.paths !== "object") {
    errors.push("Missing or invalid paths object");
  } else if (Object.keys(spec.paths).length === 0) {
    errors.push("No API paths defined");
  }

  // Validate path structure
  for (const [path, pathItem] of Object.entries(spec.paths)) {
    if (typeof pathItem !== "object" || pathItem === null) {
      errors.push(`Invalid path item for ${path}`);
      continue;
    }

    const methods = ["get", "post", "put", "delete", "patch", "head", "options", "trace"];
    const hasValidMethod = methods.some(method => method in pathItem);
    
    if (!hasValidMethod) {
      errors.push(`No valid HTTP methods defined for path ${path}`);
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
